const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 通过启动 vite build 进程来生成配置文件，然后关闭进程
 * 这样可以利用 vite 的插件系统自动生成所有需要的配置文件
 */
async function generateAutoImportConfig() {
    try {
        console.log('🚀 开始通过 vite 生成自动导入配置文件...');

        // 检查目标文件
        const targetFiles = [
            '.eslintrc-auto-import.json',
            'components.d.ts',
            'auto-imports.d.ts',
            'auto-import-business-preset.json'
        ];

        // 记录初始状态
        const initialStates = {};
        targetFiles.forEach(file => {
            const filePath = path.resolve(process.cwd(), file);
            initialStates[file] = {
                exists: fs.existsSync(filePath),
                mtime: fs.existsSync(filePath) ? fs.statSync(filePath).mtime.getTime() : 0
            };
        });

        console.log('📦 启动 vite build 进程生成配置文件...');

        return new Promise((resolve, reject) => {
            // 启动 vite build 进程
            const viteProcess = spawn('npx', ['vite', 'build'], {
                cwd: process.cwd(),
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let hasGeneratedFiles = false;
            let checkInterval;

            // 监听输出
            viteProcess.stdout.on('data', (data) => {
                const text = data.toString();
                console.log('📄', text.trim());

                // 检查是否有配置文件生成的迹象
                if (text.includes('auto-import') || text.includes('components.d.ts') || text.includes('eslintrc')) {
                    console.log('✅ 检测到配置文件生成信号');
                    if (!checkInterval) {
                        startFileCheck();
                    }
                }
            });

            viteProcess.stderr.on('data', (data) => {
                const text = data.toString();
                console.log('⚠️', text.trim());

                // 即使有错误，也检查文件是否已生成
                if (!checkInterval) {
                    startFileCheck();
                }
            });

            // 开始检查文件生成状态
            function startFileCheck() {
                if (checkInterval) return;

                console.log('🔍 开始检查配置文件生成状态...');
                checkInterval = setInterval(() => {
                    let hasNewFiles = false;

                    targetFiles.forEach(file => {
                        const filePath = path.resolve(process.cwd(), file);
                        const exists = fs.existsSync(filePath);
                        const currentMtime = exists ? fs.statSync(filePath).mtime.getTime() : 0;

                        if (exists && currentMtime > initialStates[file].mtime) {
                            hasNewFiles = true;
                            console.log(`✅ ${file} 已生成或更新`);
                        }
                    });

                    // 如果有新文件生成，等待一段时间确保所有文件都完成
                    if (hasNewFiles && !hasGeneratedFiles) {
                        hasGeneratedFiles = true;
                        console.log('⏳ 检测到文件生成，等待 3 秒确保完成...');
                        setTimeout(() => {
                            finishGeneration();
                        }, 3000);
                    }
                }, 1000);

                // 设置超时，防止无限等待
                setTimeout(() => {
                    if (!hasGeneratedFiles) {
                        console.log('⏰ 超时，强制结束进程');
                        finishGeneration();
                    }
                }, 30000); // 30秒超时
            }

            function finishGeneration() {
                if (checkInterval) {
                    clearInterval(checkInterval);
                    checkInterval = null;
                }

                console.log('🛑 终止 vite 进程...');

                // 优雅地终止进程
                viteProcess.kill('SIGTERM');

                setTimeout(() => {
                    if (!viteProcess.killed) {
                        console.log('🔨 强制终止 vite 进程...');
                        viteProcess.kill('SIGKILL');
                    }
                }, 2000);

                // 验证文件生成结果
                setTimeout(() => {
                    verifyGeneratedFiles();
                    resolve();
                }, 3000);
            }

            viteProcess.on('close', (code) => {
                if (checkInterval) {
                    clearInterval(checkInterval);
                }
                console.log(`📋 vite 进程已结束，退出码: ${code}`);
                if (!hasGeneratedFiles) {
                    verifyGeneratedFiles();
                    resolve();
                }
            });

            viteProcess.on('error', (error) => {
                if (checkInterval) {
                    clearInterval(checkInterval);
                }
                console.error('❌ vite 进程错误:', error.message);
                reject(error);
            });

            // 立即开始检查（某些情况下文件可能很快生成）
            setTimeout(() => {
                if (!checkInterval) {
                    startFileCheck();
                }
            }, 2000);
        });

    } catch (error) {
        console.error('❌ 生成配置文件失败:', error.message);
        throw error;
    }
}

function verifyGeneratedFiles() {
    console.log('🔍 验证生成的配置文件...');

    const targetFiles = [
        '.eslintrc-auto-import.json',
        'components.d.ts',
        'auto-imports.d.ts',
        'auto-import-business-preset.json'
    ];

    targetFiles.forEach(file => {
        const filePath = path.resolve(process.cwd(), file);
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            console.log(`✅ ${file} 存在 (${stats.size} bytes)`);

            // 特别检查 .eslintrc-auto-import.json 的内容
            if (file === '.eslintrc-auto-import.json') {
                try {
                    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                    const globalCount = Object.keys(content.globals || {}).length;
                    console.log(`   包含 ${globalCount} 个全局变量`);

                    if (globalCount > 100) {
                        console.log('   ✅ 配置文件内容丰富');
                    } else {
                        console.log('   ⚠️ 配置文件内容较少，可能不完整');
                    }
                } catch (e) {
                    console.log('   ❌ 配置文件格式错误');
                }
            }
        } else {
            console.log(`❌ ${file} 不存在`);
        }
    });

    console.log('🎉 配置文件生成完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
    generateAutoImportConfig().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = { generateAutoImportConfig };